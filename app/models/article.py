from datetime import datetime

from sqlalchemy import (
    <PERSON>olean,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class Article(Base):
    """文章数据模型"""

    __tablename__ = "articles"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True, nullable=True)
    content = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    cover_url = Column(String(512), nullable=True, comment="封面图URL")
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    is_published = Column(Boolean, default=False, comment="是否发布 如果未发布则显示到草稿箱中")
    is_approved = Column(Boolean, default=False, comment="是否通过审核 仅在发布后需要审核")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    # 分类
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)

    # 关联关系
    author = relationship("User", back_populates="articles")
    comments = relationship("Comment", back_populates="article")
    category = relationship("Category", back_populates="articles")
    tags = relationship("Tag", secondary="article_tags", back_populates="articles")
