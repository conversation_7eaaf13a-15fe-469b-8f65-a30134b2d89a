from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.video import Video
from app.schemas.video import VideoCreate, VideoUpdate


class CRUDVideo(CRUDBase[Video, VideoCreate, VideoUpdate]):
    async def get_by_title(self, db: AsyncSession, *, title: str) -> Video | None:
        """根据标题获取视频"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.author_id == author_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取已发布的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.is_published).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_by_folder(
        self, db: AsyncSession, *, folder_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定文件夹下的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.folder_id == folder_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def move_to_folder(
        self, db: AsyncSession, *, db_obj: Video, folder_id: int | None
    ) -> Video:
        """移动视频到指定文件夹"""
        db_obj.folder_id = folder_id
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Video, is_published: bool
    ) -> Video:
        """更新视频发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_pending_review(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的待审核视频列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            待审核视频列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(self.model.is_published & ~self.model.is_approved)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_drafts(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的草稿列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            草稿列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(~self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


video = CRUDVideo(Video)
