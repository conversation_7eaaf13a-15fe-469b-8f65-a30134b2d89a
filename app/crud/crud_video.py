from typing import Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.video import Video
from app.schemas.video import VideoCreate, VideoUpdate


class CRUDVideo(CRUDBase[Video, VideoCreate, VideoUpdate]):
    async def get_published(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> list[Video]:
        """获取已发布且已审核的视频列表

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            视频列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.is_published & self.model.is_approved)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的视频列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            视频列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    

   



video = CRUDVideo(Video)