from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import models
from app.crud.base import CRUDBase
from app.models.article import Article
from app.schemas.article import ArticleCreate, ArticleUpdate


class CRUDArticle(CRUDBase[Article, ArticleCreate, ArticleUpdate]):
 
    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取指定作者的文章列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            文章列表
        """
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.category).selectinload(models.Category.children),
            )
            .where(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    

article = CRUDArticle(Article)
