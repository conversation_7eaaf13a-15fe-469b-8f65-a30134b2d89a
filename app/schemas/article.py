from datetime import datetime

from pydantic import BaseModel, field_serializer

from app.schemas.tag import TagBase
from app.schemas.user import UserResponse


class ArticleBase(BaseModel):
    """文章基础模型"""

    title: str | None = None  # 标题
    content: str | None = None  # 内容
    description: str | None = None  # 描述
    cover_url: str | None = None  # 封面图URL
    is_published: bool = False  # 是否发布（草稿状态为False）
    is_approved: bool = False  # 是否通过审核
    tags: list[TagBase] | None = None  # 标签列表


class ArticleCreate(BaseModel):
    """创建文章的请求模型"""

    author_id: str
    title: str | None = None  # 可选标题，如果不提供则使用默认值
    content: str | None = None  # 可选内容，如果不提供则使用默认值


class ArticleUpdate(BaseModel):
    """更新文章的请求模型"""

    title: str | None = None
    content: str | None = None
    description: str | None = None
    cover_url: str | None = None
    is_published: bool | None = None
    tags: list[str] | None = None
    category_id: int | None = None


class ArticleInDBBase(ArticleBase):
    """数据库中文章的基础模型"""

    id: int
    author_id: int
    category_id: int | None = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Article(ArticleInDBBase):
    """API响应中的文章模型"""

    # category: Category | None = None

    # # 删除category_id 字段
    # @field_serializer("category_id")
    # def serialize_category_id(self, category_id: int | None) -> None:
    #     return None

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class ArticleWithAuthor(Article):
    """包含作者信息的文章模型"""

    author: UserResponse


class ArticleWithStats(ArticleInDBBase):
    """包含统计信息的文章模型"""

    like_count: int = 0
    favorite_count: int = 0
    is_liked_by_user: bool = False
    is_favorited_by_user: bool = False


class ArticleList(BaseModel):
    """文章列表响应模型"""

    total: int
    items: list[Article]


class ArticleListWithAuthor(BaseModel):
    total: int
    items: list[ArticleWithAuthor]


class ArticleListWithStats(BaseModel):
    """包含统计信息的文章列表响应模型"""

    total: int
    items: list[ArticleWithStats]
