# 使schemas目录成为一个Python包
from app.schemas.article import (
    Article,
    ArticleCreate,
    ArticleList,
    ArticleListWithAuthor,
    ArticleListWithStats,
    ArticleUpdate,
    ArticleWithAuthor,
    ArticleWithStats,
)
from app.schemas.comment import Comment, CommentCreate, CommentList, CommentUpdate
from app.schemas.favorite import (
    ContentFavoriteInfo,
    Favorite,
    FavoriteBase,
    FavoriteCreate,
    FavoriteHistory,
    FavoriteStats,
    FavoriteStatus,
    FavoriteToggle,
    FavoriteUpdate,
    FavoriteWithContent,
)
from app.schemas.file_hash import FileHash, FileHashBase, FileHashCreate, FileHashUpdate
from app.schemas.like import (
    ContentLikeInfo,
    Like,
    LikeBase,
    LikeCreate,
    LikeHistory,
    LikeStats,
    LikeStatus,
    LikeToggle,
)
from app.schemas.recommendation import (
    ContentSimilarity,
    ContentSimilarityCreate,
    HotContentResponse,
    RecommendationFeedback,
    RecommendationItem,
    RecommendationLog,
    RecommendationLogCreate,
    RecommendationRequest,
    RecommendationResponse,
    RecommendationStats,
    SimilarContentResponse,
    UserBrowseHistory,
    UserBrowseHistoryCreate,
    UserInteraction,
    UserInteractionCreate,
    UserInterestProfile,
    UserProfile,
    UserProfileCreate,
    UserProfileUpdate,
)
from app.schemas.review import Review, ReviewCreate, ReviewList, ReviewUpdate
from app.schemas.user import (
    UserBase,
    UserCreate,
    UserInDB,
    UserInDBBase,
    UserResponse,
    UserUpdate,
)
from app.schemas.video import (
    Video,
    VideoCreate,
    VideoList,
    VideoListWithStats,
    VideoUpdate,
    VideoWithStats,
)

# 在这里导入所有模式，以便在其他地方可以通过app.schemas导入
