from typing import Any, Literal

from fastapi import HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app import crud, models, schemas


class ContentService:
    """内容服务，处理文章和视频的通用逻辑"""

    def __init__(self, content_type: Literal["article", "video"]) -> None:
        """初始化内容服务

        Args:
            content_type: 内容类型，article或video
        """
        self.content_type = content_type
        self.crud = crud.article if content_type == "article" else crud.video
        self.model = models.Article if content_type == "article" else models.Video
        self.schema = schemas.Article if content_type == "article" else schemas.Video

    async def check_permission(
        self, db: AsyncSession, content_id: int, current_user: models.User
    ) -> Any:
        """检查用户是否有权限访问内容

        Args:
            db: 数据库会话
            content_id: 内容ID
            current_user: 当前用户

        Returns:
            内容对象

        Raises:
            HTTPException: 内容不存在或无权限访问
        """
        query = (
            select(self.model)
            .options(
                joinedload(self.model.tags),
                joinedload(self.model.author),
            )
            .where(self.model.id == content_id)
        )
        result = await db.execute(query)
        content = result.scalars().first()

        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{self.content_type}不存在",
            )
        # 非管理员且非作者只能查看已发布的内容
        if (
            not content.is_published
            and not content.is_approved
            and not await crud.user.is_admin(current_user)
            and content.author_id != current_user.id
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"没有权限查看该{self.content_type}",
            )
        return content

    async def check_update_permission(
        self, db: AsyncSession, content_id: int, current_user: models.User
    ) -> Any:
        """检查用户是否有权限更新内容

        Args:
            db: 数据库会话
            content_id: 内容ID
            current_user: 当前用户

        Returns:
            内容对象

        Raises:
            HTTPException: 内容不存在或无权限更新
        """
        query = (
            select(self.model)
            .options(
                joinedload(self.model.tags),
            )
            .where(self.model.id == content_id)
        )
        result = await db.execute(query)
        content = result.scalars().first()

        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{self.content_type}不存在",
            )
        # 只有管理员和作者可以更新内容
        if not await crud.user.is_admin(current_user) and content.author_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"没有权限更新该{self.content_type}",
            )
        return content

    async def handle_publish_status(
        self, db: AsyncSession, content: Any, is_published: bool | None = None
    ) -> Any:
        """处理内容的发布状态

        Args:
            db: 数据库会话
            content: 内容对象
            is_published: 是否发布

        Returns:
            更新后的内容对象
        """
        if is_published is None:
            return content

        # 如果内容从未发布变为发布，需要创建审核记录
        if not content.is_published and is_published:
            content.is_approved = False
            content.is_published = True
            db.add(content)
            await db.commit()
            await db.refresh(content)

            review_in = schemas.ReviewCreate(
                content_type=self.content_type,
                content_id=content.id,
            )
            await crud.review.create(db=db, obj_in=review_in)

        # 如果内容从发布变为未发布，删除审核记录
        elif content.is_published and not is_published:
            content.is_published = False
            content.is_approved = False
            db.add(content)
            await db.commit()
            await db.refresh(content)

            review = await crud.review.get_by_content(
                db, content_type=self.content_type, content_id=content.id
            )
            if review:
                await crud.review.remove(db=db, id=review.id)

        return content

    async def handle_content_update(
        self, db: AsyncSession, content: Any, has_content_update: bool
    ) -> Any:
        """处理内容更新后的审核状态

        Args:
            db: 数据库会话
            content: 内容对象
            has_content_update: 是否有内容更新

        Returns:
            更新后的内容对象
        """
        if not content.is_published or not has_content_update:
            return content

        # 已发布内容有更新，需要重新审核
        content.is_approved = False
        db.add(content)
        await db.commit()
        await db.refresh(content)

        # 更新审核记录
        review = await crud.review.get_by_content(
            db, content_type=self.content_type, content_id=content.id
        )
        if review:
            await crud.review.update(
                db=db,
                db_obj=review,
                obj_in={"status": "pending", "reviewer_id": None, "reviewed_at": None},
            )

        return content

    async def handle_tags_update(
        self, db: AsyncSession, content: Any, tags: list[str] | None = None
    ) -> Any:
        """处理内容标签的更新

        Args:
            db: 数据库会话
            content: 内容对象
            tags: 标签名列表

        Returns:
            更新后的内容对象
        """
        if tags is None:
            return content

        # 获取或创建标签
        tag_objects = await crud.tag.get_or_create_multi(db, names=tags)

        # 更新内容的标签关系
        content.tags = tag_objects
        db.add(content)
        await db.commit()
        await db.refresh(content, attribute_names=["tags"])

        return content


article_service = ContentService("article")
video_service = ContentService("video")
