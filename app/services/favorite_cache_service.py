"""收藏缓存服务"""

import json
from typing import Any

from app.db.redis import get_redis
from app.services.logger import get_logger

logger = get_logger(__name__)


class FavoriteCacheService:
    """收藏缓存服务类"""

    def __init__(self):
        self.redis_client = None
        self.favorite_count_prefix = "favorite_count"  # 收藏数量缓存
        self.user_favorite_prefix = "user_favorite"  # 用户收藏状态缓存
        self.hot_favorite_prefix = "hot_favorite"  # 热门收藏内容缓存
        self.cache_expire = 3600  # 缓存过期时间（秒）
        self.hot_cache_expire = 1800  # 热点数据缓存过期时间（秒）

    async def _ensure_redis_client(self):
        """确保 Redis 客户端已初始化"""
        if self.redis_client is None:
            self.redis_client = await get_redis()

    async def get_favorite_count(self, content_type: str, content_id: int) -> int | None:
        """从缓存获取收藏数量"""
        try:
            await self._ensure_redis_client()
            key = self._get_favorite_count_key(content_type, content_id)
            count = await self.redis_client.get(key)
            return int(count) if count is not None else None
        except Exception as e:
            logger.error(f"获取收藏数量缓存失败: {e}")
            return None

    def _get_favorite_count_key(self, content_type: str, content_id: int) -> str:
        """获取收藏数量缓存键"""
        return f"{self.favorite_count_prefix}:{content_type}:{content_id}"

    def _get_user_favorite_key(self, user_id: int, content_type: str, content_id: int) -> str:
        """获取用户收藏状态缓存键"""
        return f"{self.user_favorite_prefix}:{user_id}:{content_type}:{content_id}"

    def _get_user_favorites_set_key(self, user_id: int, content_type: str) -> str:
        """获取用户收藏集合缓存键"""
        return f"{self.user_favorite_prefix}_set:{user_id}:{content_type}"

    def _get_hot_favorite_key(self, content_type: str) -> str:
        """获取热门收藏内容缓存键"""
        return f"{self.hot_favorite_prefix}:{content_type}"

    async def set_favorite_count(self, content_type: str, content_id: int, count: int) -> bool:
        """设置收藏数量缓存"""
        try:
            await self._ensure_redis_client()
            key = self._get_favorite_count_key(content_type, content_id)
            await self.redis_client.setex(key, self.cache_expire, count)
            return True
        except Exception as e:
            logger.error(f"设置收藏数量缓存失败: {e}")
            return False

    async def increment_favorite_count(self, content_type: str, content_id: int) -> int | None:
        """增加收藏数量"""
        try:
            await self._ensure_redis_client()
            key = self._get_favorite_count_key(content_type, content_id)
            new_count = await self.redis_client.incr(key)
            await self.redis_client.expire(key, self.cache_expire)
            return new_count
        except Exception as e:
            logger.error(f"增加收藏数量失败: {e}")
            return None

    async def decrement_favorite_count(self, content_type: str, content_id: int) -> int | None:
        """减少收藏数量"""
        try:
            await self._ensure_redis_client()
            key = self._get_favorite_count_key(content_type, content_id)
            new_count = await self.redis_client.decr(key)
            # 确保不会小于0
            if new_count < 0:
                await self.redis_client.set(key, 0)
                new_count = 0
            await self.redis_client.expire(key, self.cache_expire)
            return new_count
        except Exception as e:
            logger.error(f"减少收藏数量失败: {e}")
            return None

    async def is_favorited_by_user(
        self, user_id: int, content_type: str, content_id: int
    ) -> bool | None:
        """检查用户是否已收藏"""
        try:
            await self._ensure_redis_client()
            key = self._get_user_favorite_key(user_id, content_type, content_id)
            result = await self.redis_client.get(key)
            return result == b"1" if result is not None else None
        except Exception as e:
            logger.error(f"检查用户收藏状态失败: {e}")
            return None

    async def set_user_favorite_status(
        self, user_id: int, content_type: str, content_id: int, is_favorited: bool
    ) -> bool:
        """设置用户收藏状态"""
        try:
            await self._ensure_redis_client()
            key = self._get_user_favorite_key(user_id, content_type, content_id)
            value = "1" if is_favorited else "0"
            await self.redis_client.setex(key, self.cache_expire, value)

            # 同时更新用户收藏集合
            set_key = self._get_user_favorites_set_key(user_id, content_type)
            if is_favorited:
                await self.redis_client.sadd(set_key, content_id)
            else:
                await self.redis_client.srem(set_key, content_id)
            await self.redis_client.expire(set_key, self.cache_expire)

            return True
        except Exception as e:
            logger.error(f"设置用户收藏状态失败: {e}")
            return False

    async def get_user_favorited_contents(self, user_id: int, content_type: str) -> set[int] | None:
        """获取用户收藏的内容ID集合"""
        try:
            await self._ensure_redis_client()
            key = self._get_user_favorites_set_key(user_id, content_type)
            content_ids = await self.redis_client.smembers(key)
            return {int(cid) for cid in content_ids} if content_ids else set()
        except Exception as e:
            logger.error(f"获取用户收藏内容失败: {e}")
            return None

    async def batch_get_favorite_info(
        self, content_items: list[tuple[str, int]], user_id: int | None = None
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """批量获取收藏信息"""
        result = {}

        try:
            await self._ensure_redis_client()
            # 批量获取收藏数量
            pipe = await self.redis_client.pipeline()
            for content_type, content_id in content_items:
                key = self._get_favorite_count_key(content_type, content_id)
                pipe.get(key)

            favorite_counts = await pipe.execute()

            # 如果需要获取用户收藏状态
            user_favorite_statuses = []
            if user_id:
                pipe = await self.redis_client.pipeline()
                for content_type, content_id in content_items:
                    key = self._get_user_favorite_key(user_id, content_type, content_id)
                    pipe.get(key)
                user_favorite_statuses = await pipe.execute()

            # 组装结果
            for i, (content_type, content_id) in enumerate(content_items):
                favorite_count = favorite_counts[i]
                favorite_count = int(favorite_count) if favorite_count is not None else None

                is_favorited = False
                if user_id and i < len(user_favorite_statuses):
                    status = user_favorite_statuses[i]
                    is_favorited = status == b"1" if status is not None else None

                result[(content_type, content_id)] = {
                    "favorite_count": favorite_count,
                    "is_favorited": is_favorited,
                }

        except Exception as e:
            logger.error(f"批量获取收藏信息失败: {e}")

        return result

    async def update_hot_content(
        self, content_type: str, content_rankings: list[dict[str, Any]]
    ) -> bool:
        """更新热点内容排行"""
        try:
            await self._ensure_redis_client()
            key = self._get_hot_content_key(content_type)
            data = json.dumps(content_rankings, ensure_ascii=False)
            await self.redis_client.setex(key, self.hot_cache_expire, data)
            return True
        except Exception as e:
            logger.error(f"更新热点内容失败: {e}")
            return False

    async def get_hot_content(self, content_type: str) -> list[dict[str, Any]] | None:
        """获取热点内容排行"""
        try:
            await self._ensure_redis_client()
            key = self._get_hot_content_key(content_type)
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data.decode("utf-8"))
            return None
        except Exception as e:
            logger.error(f"获取热点内容失败: {e}")
            return None

    async def clear_content_cache(self, content_type: str, content_id: int) -> bool:
        """清除特定内容的缓存"""
        try:
            await self._ensure_redis_client()
            # 清除收藏数量缓存
            favorite_count_key = self._get_favorite_count_key(content_type, content_id)
            await self.redis_client.delete(favorite_count_key)

            return True
        except Exception as e:
            logger.error(f"清除内容缓存失败: {e}")
            return False

    async def clear_user_cache(self, user_id: int) -> bool:
        """清除用户相关缓存"""
        try:
            await self._ensure_redis_client()
            # 清除用户收藏集合缓存
            for content_type in ["article", "video"]:
                set_key = self._get_user_favorites_set_key(user_id, content_type)
                await self.redis_client.delete(set_key)

            return True
        except Exception as e:
            logger.error(f"清除用户缓存失败: {e}")
            return False


# 创建全局实例
favorite_cache_service = FavoriteCacheService()
