"""
权限常量定义模块

定义系统中所有权限相关的常量，包括角色、权限代码等
"""

from enum import Enum
from typing import Dict, List, Set


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "管理员"
    USER = "普通用户"
    GUEST = "访客"


class ArticlePermission(str, Enum):
    """文章权限枚举"""
    # 基础权限
    READ_PUBLIC = "articles:read_public"          # 查看公开文章
    READ_ALL = "articles:read_all"                # 查看所有文章
    READ_OWN = "articles:read_own"                # 查看自己的文章
    READ_DRAFT = "articles:read_draft"            # 查看草稿
    READ_PENDING = "articles:read_pending"        # 查看待审核文章
    
    # 创建和编辑权限
    CREATE = "articles:create"                    # 创建文章
    UPDATE_OWN = "articles:update_own"            # 更新自己的文章
    UPDATE_ALL = "articles:update_all"            # 更新所有文章
    
    # 发布和审核权限
    PUBLISH_OWN = "articles:publish_own"          # 发布自己的文章
    PUBLISH_ALL = "articles:publish_all"          # 发布所有文章
    APPROVE = "articles:approve"                  # 审核文章
    
    # 删除权限
    DELETE_OWN = "articles:delete_own"            # 删除自己的文章
    DELETE_ALL = "articles:delete_all"            # 删除所有文章
    
    # 管理权限
    MANAGE = "articles:manage"                    # 文章管理（包含所有权限）


class ArticleStatus(str, Enum):
    """文章状态枚举"""
    DRAFT = "draft"                               # 草稿
    PUBLISHED = "published"                       # 已发布
    PENDING_REVIEW = "pending_review"             # 待审核
    APPROVED = "approved"                         # 已审核通过
    REJECTED = "rejected"                         # 审核被拒


class ArticleVisibility(str, Enum):
    """文章可见性枚举"""
    PUBLIC = "public"                             # 公开（所有人可见）
    PRIVATE = "private"                           # 私有（仅作者可见）
    MEMBERS_ONLY = "members_only"                 # 仅会员可见


# 角色权限映射
ROLE_PERMISSIONS: Dict[UserRole, Set[str]] = {
    # 管理员：拥有所有权限
    UserRole.ADMIN: {
        ArticlePermission.READ_PUBLIC,
        ArticlePermission.READ_ALL,
        ArticlePermission.READ_OWN,
        ArticlePermission.READ_DRAFT,
        ArticlePermission.READ_PENDING,
        ArticlePermission.CREATE,
        ArticlePermission.UPDATE_OWN,
        ArticlePermission.UPDATE_ALL,
        ArticlePermission.PUBLISH_OWN,
        ArticlePermission.PUBLISH_ALL,
        ArticlePermission.APPROVE,
        ArticlePermission.DELETE_OWN,
        ArticlePermission.DELETE_ALL,
        ArticlePermission.MANAGE,
    },
    
    # 普通用户：基础权限
    UserRole.USER: {
        ArticlePermission.READ_PUBLIC,
        ArticlePermission.READ_OWN,
        ArticlePermission.READ_DRAFT,
        ArticlePermission.READ_PENDING,
        ArticlePermission.CREATE,
        ArticlePermission.UPDATE_OWN,
        ArticlePermission.PUBLISH_OWN,
        ArticlePermission.DELETE_OWN,
    },
    
    # 游客：仅查看公开内容
    UserRole.GUEST: {
        ArticlePermission.READ_PUBLIC,
    },
}


# 文章状态访问权限映射
ARTICLE_STATUS_ACCESS: Dict[ArticleStatus, Set[str]] = {
    # 草稿：仅作者和管理员可见
    ArticleStatus.DRAFT: {
        ArticlePermission.READ_DRAFT,
        ArticlePermission.READ_ALL,
        ArticlePermission.MANAGE,
    },
    
    # 待审核：作者和管理员可见
    ArticleStatus.PENDING_REVIEW: {
        ArticlePermission.READ_PENDING,
        ArticlePermission.READ_ALL,
        ArticlePermission.MANAGE,
    },
    
    # 已发布但未审核：作者和管理员可见
    ArticleStatus.PUBLISHED: {
        ArticlePermission.READ_PENDING,
        ArticlePermission.READ_ALL,
        ArticlePermission.MANAGE,
    },
    
    # 已审核通过：所有人可见
    ArticleStatus.APPROVED: {
        ArticlePermission.READ_PUBLIC,
        ArticlePermission.READ_OWN,
        ArticlePermission.READ_ALL,
        ArticlePermission.MANAGE,
    },
}


def get_user_role_from_user(user) -> UserRole:
    """从用户对象获取用户角色"""
    if not user:
        return UserRole.GUEST
    
    if user.is_superuser:
        return UserRole.ADMIN
    
    if user.role and user.role.name == UserRole.ADMIN:
        return UserRole.ADMIN
    
    return UserRole.USER


def get_user_permissions(user) -> Set[str]:
    """获取用户的所有权限"""
    role = get_user_role_from_user(user)
    return ROLE_PERMISSIONS.get(role, set())


def has_permission(user, permission: str) -> bool:
    """检查用户是否拥有指定权限"""
    user_permissions = get_user_permissions(user)
    return permission in user_permissions


def get_article_status(article) -> ArticleStatus:
    """获取文章状态"""
    if not article.is_published:
        return ArticleStatus.DRAFT
    elif article.is_published and not article.is_approved:
        return ArticleStatus.PENDING_REVIEW
    elif article.is_published and article.is_approved:
        return ArticleStatus.APPROVED
    else:
        return ArticleStatus.PUBLISHED


def can_access_article_by_status(user, article_status: ArticleStatus) -> bool:
    """根据文章状态检查用户是否可以访问"""
    user_permissions = get_user_permissions(user)
    required_permissions = ARTICLE_STATUS_ACCESS.get(article_status, set())
    
    # 检查用户是否拥有任一所需权限
    return bool(user_permissions & required_permissions)
