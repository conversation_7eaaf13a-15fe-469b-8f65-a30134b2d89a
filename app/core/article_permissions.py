"""
文章权限检查装饰器和工具函数

提供用于检查文章权限的装饰器和工具函数
"""

from functools import wraps
from typing import Callable, Optional

from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.api import deps
from app.core.permissions import (
    ArticlePermission,
    ArticleStatus,
    UserRole,
    can_access_article_by_status,
    get_article_status,
    get_user_role_from_user,
    has_permission,
)


def require_article_permission(permission: str):
    """
    装饰器：要求用户拥有指定的文章权限
    
    Args:
        permission: 所需的权限代码
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            if not current_user and 'current_user' in func.__annotations__:
                # 如果没有在kwargs中找到，尝试从args中获取
                for i, (param_name, param_type) in enumerate(func.__annotations__.items()):
                    if param_name == 'current_user' and i < len(args):
                        current_user = args[i]
                        break
            
            if not has_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要 {permission} 权限",
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_article_access(article_id_param: str = "article_id"):
    """
    装饰器：检查用户是否有权限访问指定文章
    
    Args:
        article_id_param: 文章ID参数名称
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取文章ID
            article_id = kwargs.get(article_id_param)
            if article_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="缺少文章ID参数",
                )
            
            # 获取数据库会话和当前用户
            db = kwargs.get('db')
            current_user = kwargs.get('current_user')
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="数据库会话未找到",
                )
            
            # 检查文章访问权限
            article = await check_article_access_permission(db, article_id, current_user)
            
            # 将文章对象添加到kwargs中，供后续使用
            kwargs['article'] = article
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def check_article_access_permission(
    db: AsyncSession, 
    article_id: int, 
    current_user: Optional[models.User] = None
) -> models.Article:
    """
    检查用户是否有权限访问指定文章
    
    Args:
        db: 数据库会话
        article_id: 文章ID
        current_user: 当前用户（可为None，表示游客）
    
    Returns:
        文章对象
        
    Raises:
        HTTPException: 文章不存在或无权限访问
    """
    # 获取文章
    article = await crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    
    # 获取文章状态
    article_status = get_article_status(article)
    
    # 检查基础访问权限
    if not can_access_article_by_status(current_user, article_status):
        # 如果是作者，允许访问自己的文章
        if current_user and article.author_id == current_user.id:
            if has_permission(current_user, ArticlePermission.READ_OWN):
                return article
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该文章",
        )
    
    return article


async def check_article_update_permission(
    db: AsyncSession, 
    article_id: int, 
    current_user: models.User
) -> models.Article:
    """
    检查用户是否有权限更新指定文章
    
    Args:
        db: 数据库会话
        article_id: 文章ID
        current_user: 当前用户
    
    Returns:
        文章对象
        
    Raises:
        HTTPException: 文章不存在或无权限更新
    """
    # 获取文章
    article = await crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    
    # 检查更新权限
    user_role = get_user_role_from_user(current_user)
    
    if user_role == UserRole.ADMIN:
        # 管理员可以更新所有文章
        if has_permission(current_user, ArticlePermission.UPDATE_ALL):
            return article
    
    # 作者可以更新自己的文章
    if article.author_id == current_user.id:
        if has_permission(current_user, ArticlePermission.UPDATE_OWN):
            return article
    
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="没有权限更新该文章",
    )


async def check_article_delete_permission(
    db: AsyncSession, 
    article_id: int, 
    current_user: models.User
) -> models.Article:
    """
    检查用户是否有权限删除指定文章
    
    Args:
        db: 数据库会话
        article_id: 文章ID
        current_user: 当前用户
    
    Returns:
        文章对象
        
    Raises:
        HTTPException: 文章不存在或无权限删除
    """
    # 获取文章
    article = await crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    
    # 检查删除权限
    user_role = get_user_role_from_user(current_user)
    
    if user_role == UserRole.ADMIN:
        # 管理员可以删除所有文章
        if has_permission(current_user, ArticlePermission.DELETE_ALL):
            return article
    
    # 作者可以删除自己的文章
    if article.author_id == current_user.id:
        if has_permission(current_user, ArticlePermission.DELETE_OWN):
            return article
    
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="没有权限删除该文章",
    )


def get_current_user_optional():
    """
    获取当前用户（可选）
    用于支持游客访问的端点
    """
    async def _get_current_user_optional(
        db: AsyncSession = Depends(deps.get_db),
        token: Optional[str] = Depends(deps.oauth2_scheme_optional)
    ) -> Optional[models.User]:
        if not token:
            return None
        
        try:
            return await deps.get_current_user(db=db, token=token)
        except HTTPException:
            # 如果token无效，返回None（游客）
            return None
    
    return _get_current_user_optional


async def filter_articles_by_permission(
    articles: list[models.Article], 
    current_user: Optional[models.User] = None
) -> list[models.Article]:
    """
    根据用户权限过滤文章列表
    
    Args:
        articles: 文章列表
        current_user: 当前用户（可为None）
    
    Returns:
        过滤后的文章列表
    """
    filtered_articles = []
    
    for article in articles:
        article_status = get_article_status(article)
        
        # 检查基础访问权限
        if can_access_article_by_status(current_user, article_status):
            filtered_articles.append(article)
        # 如果是作者，允许访问自己的文章
        elif current_user and article.author_id == current_user.id:
            if has_permission(current_user, ArticlePermission.READ_OWN):
                filtered_articles.append(article)
    
    return filtered_articles
