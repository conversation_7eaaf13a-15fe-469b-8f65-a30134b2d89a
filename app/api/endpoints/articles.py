from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import crud, models, schemas
from app.api import deps
from app.services.content_service import article_service

router = APIRouter()


@router.post("/", response_model=schemas.Article)
async def create_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新文章"""
    # 用户点击编辑自动为草稿，后续保存皆为更新操作
    article = await crud.article.create(
        db=db,
        obj_in={
            "author_id": current_user.id,
            "title": "未命名文章",  # 设置默认标题
            "content": "",  # 设置空内容
        },
    )
    return article


@router.get("/", response_model=schemas.ArticleList)
async def read_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User | None = Depends(deps.get_current_active_user),
    show_draft: bool = False,  # 是否显示草稿
    show_pending: bool = False,  # 是否显示待审核
) -> Any:
    """获取文章列表
    - 管理员：可以看到所有文章
    - 普通用户：
      - 默认只能看到已发布且已审核的文章
      - 可以通过参数查看自己的草稿和待审核文章
    """
    is_admin = await crud.user.is_admin(current_user)

    if is_admin:
        # 管理员可以看到所有文章
        articles = await crud.article.get_multi(
            db,
            skip=skip,
            limit=limit,
            options=[
                selectinload(models.Article.tags),
            ],
        )
        total = db.query(models.Article).count()
    else:
        # 构建查询条件
        conditions = []

        # 已发布且已审核的文章对所有人可见
        conditions.append(models.Article.is_published & models.Article.is_approved)

        # 如果要显示草稿，添加未发布的文章条件
        if show_draft:
            conditions.append(
                ~models.Article.is_published & (models.Article.author_id == current_user.id)
            )

        # 如果要显示待审核，添加已发布未审核的文章条件
        if show_pending:
            conditions.append(
                models.Article.is_published
                & ~models.Article.is_approved
                & (models.Article.author_id == current_user.id)
            )

        # 使用 OR 连接所有条件
        query = (
            db.query(models.Article)
            .options(
                selectinload(models.Article.tags),
                selectinload(models.Article.category).selectinload(models.Category.children),
            )
            .filter(db.or_(*conditions))
        )

        # 获取分页数据
        articles = await query.offset(skip).limit(limit).all()
        total = query.count()

    return {"total": total, "items": articles}


@router.get("/my", response_model=schemas.ArticleList)
async def read_my_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的文章列表"""
    articles = await crud.article.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )
    total = await db.scalar(
        select(func.count())
        .select_from(models.Article)
        .where(models.Article.author_id == current_user.id)
    )
    return {"total": total, "items": articles}


@router.get("/{article_id}", response_model=schemas.ArticleWithAuthor)
async def read_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    current_user: models.User | None = Depends(deps.get_current_active_user),
) -> Any:
    """获取文章详情"""
    article = await article_service.check_permission(db, article_id, current_user)
    return article


@router.put("/{article_id}", response_model=schemas.Article)
async def update_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    article_in: schemas.ArticleUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新文章
    - 如果文章从未发布变为发布，需要创建审核记录
    - 如果已发布文章的内容有更新，需要重新审核
    - 未发布的文章（草稿）可以自由修改，不需要审核
    """
    # 检查更新权限
    article = await article_service.check_update_permission(db, article_id, current_user)

    article_in_dict = article_in.dict(exclude_unset=True)

    # 处理发布状态变更
    if "is_published" in article_in_dict:
        article = await article_service.handle_publish_status(
            db=db, content=article, is_published=article_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(article_in.title or article_in.content)
    article = await article_service.handle_content_update(
        db=db, content=article, has_content_update=has_content_update
    )

    # 处理标签更新
    if article_in.tags is not None:
        article = await article_service.handle_tags_update(
            db=db, content=article, tags=article_in.tags
        )
        # 标签已由服务处理，从字典中移除以避免在基础CRUD中重复处理
        del article_in_dict["tags"]

    # 更新文章
    article = await crud.article.update(db=db, db_obj=article, obj_in=article_in_dict)
    return article


@router.delete("/{article_id}", response_model=schemas.Article)
async def delete_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """删除文章"""
    article = await crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 只有管理员和作者可以删除文章
    if not await crud.user.is_admin(current_user) and article.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除该文章",
        )
    # 删除关联的审核记录
    review = await crud.review.get_by_content(db, content_type="article", content_id=article.id)
    if review:
        await crud.review.remove(db=db, id=review.id)
    article = await crud.article.remove(db=db, id=article_id)
    return article


@router.get("/with-stats", response_model=schemas.ArticleListWithStats)
async def read_articles_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取包含统计信息的文章列表"""
    from app.services.favorite_cache_service import favorite_cache_service
    from app.services.like_cache_service import like_cache_service

    # 管理员可以看到所有文章，普通用户只能看到已发布的文章
    if await crud.user.is_admin(current_user):
        articles = await crud.article.get_multi(
            db,
            skip=skip,
            limit=limit,
            options=[
                selectinload(models.Article.tags),
                selectinload(models.Article.category).selectinload(models.Category.children),
            ],
        )
        total = db.query(models.Article).count()
    else:
        articles = await crud.article.get_published(db, skip=skip, limit=limit)
        total = db.query(models.Article).filter(models.Article.is_published).count()

    # 批量获取点赞和收藏信息
    content_items = [("article", article.id) for article in articles]

    like_info = await like_cache_service.batch_get_like_info(content_items, current_user.id)
    favorite_info = await favorite_cache_service.batch_get_favorite_info(
        content_items, current_user.id
    )

    # 从数据库获取缺失的数据
    missing_like_items = []
    missing_favorite_items = []

    for item_key in content_items:
        like_data = like_info.get(item_key, {})
        if like_data.get("like_count") is None or like_data.get("is_liked") is None:
            missing_like_items.append(item_key)

        favorite_data = favorite_info.get(item_key, {})
        if favorite_data.get("favorite_count") is None or favorite_data.get("is_favorited") is None:
            missing_favorite_items.append(item_key)

    if missing_like_items:
        db_like_info = await crud.like.get_content_likes_batch(
            db, content_items=missing_like_items, user_id=current_user.id
        )
        like_info.update(db_like_info)

    if missing_favorite_items:
        db_favorite_info = await crud.favorite.get_content_favorites_batch(
            db, content_items=missing_favorite_items, user_id=current_user.id
        )
        favorite_info.update(db_favorite_info)

    # 构建响应
    articles_with_stats = []
    for article in articles:
        item_key = ("article", article.id)
        like_data = like_info.get(item_key, {})
        favorite_data = favorite_info.get(item_key, {})

        article_dict = {
            **article.__dict__,
            "like_count": like_data.get("like_count", 0),
            "favorite_count": favorite_data.get("favorite_count", 0),
            "is_liked_by_user": like_data.get("is_liked", False),
            "is_favorited_by_user": favorite_data.get("is_favorited", False),
        }
        articles_with_stats.append(schemas.ArticleWithStats(**article_dict))

    return {"total": total, "items": articles_with_stats}


@router.get("/{article_id}/with-stats", response_model=schemas.ArticleWithStats)
async def read_article_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取包含统计信息的文章详情"""
    from app.services.favorite_cache_service import favorite_cache_service
    from app.services.like_cache_service import like_cache_service

    article = await crud.article.get(
        db,
        id=article_id,
        options=[
            selectinload(models.Article.tags),
            selectinload(models.Article.category).selectinload(models.Category.children),
        ],
    )
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 非管理员且非作者只能查看已发布的文章
    if (
        not article.is_published
        and not await crud.user.is_admin(current_user)
        and article.author_id != current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该文章",
        )

    # 获取点赞和收藏信息
    like_count = await like_cache_service.get_like_count("article", article_id)
    if like_count is None:
        like_count = await crud.like.get_content_like_count(
            db, content_type="article", content_id=article_id
        )
        await like_cache_service.set_like_count("article", article_id, like_count)

    favorite_count = await favorite_cache_service.get_favorite_count("article", article_id)
    if favorite_count is None:
        favorite_count = await crud.favorite.get_content_favorite_count(
            db, content_type="article", content_id=article_id
        )
        await favorite_cache_service.set_favorite_count("article", article_id, favorite_count)

    is_liked = await like_cache_service.is_liked_by_user(current_user.id, "article", article_id)
    if is_liked is None:
        is_liked = await crud.like.is_liked_by_user(
            db, user_id=current_user.id, content_type="article", content_id=article_id
        )
        await like_cache_service.set_user_like_status(
            current_user.id, "article", article_id, is_liked
        )

    is_favorited = await favorite_cache_service.is_favorited_by_user(
        current_user.id, "article", article_id
    )
    if is_favorited is None:
        is_favorited = await crud.favorite.is_favorited_by_user(
            db, user_id=current_user.id, content_type="article", content_id=article_id
        )
        await favorite_cache_service.set_user_favorite_status(
            current_user.id, "article", article_id, is_favorited
        )

    # 构建响应
    article_dict = {
        **article.__dict__,
        "like_count": like_count,
        "favorite_count": favorite_count,
        "is_liked_by_user": is_liked,
        "is_favorited_by_user": is_favorited,
    }

    return schemas.ArticleWithStats(**article_dict)
