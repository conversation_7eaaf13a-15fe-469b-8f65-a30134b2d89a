from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any

from app import crud, models, schemas
from app.api import deps
from app.services.content_service import video_service

router = APIRouter()


@router.post("/", response_model=schemas.Video)
async def create_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_in: schemas.VideoCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新视频
    - 未发布的视频（草稿）不需要审核
    - 发布的视频需要审核
    """
    video = await crud.video.create(db=db, obj_in=video_in)
    # 处理发布状态
    video = await video_service.handle_publish_status(
        db=db, content=video, is_published=video.is_published
    )
    return video


@router.get("/", response_model=schemas.VideoList)
async def read_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
    show_draft: bool = False,  # 是否显示草稿
    show_pending: bool = False,  # 是否显示待审核
) -> Any:
    """获取视频列表
    - 管理员：可以看到所有视频
    - 普通用户：
      - 默认只能看到已发布且已审核的视频
      - 可以通过参数查看自己的草稿和待审核视频
    """
    is_admin = crud.user.is_admin(current_user)
    
    if is_admin:
        # 管理员可以看到所有视频
        videos = await crud.video.get_multi(db, skip=skip, limit=limit)
        result = await db.execute(select(models.Video))
        total = len(result.scalars().all())
    else:
        # 构建查询条件
        conditions = []
        
        # 已发布且已审核的视频对所有人可见
        conditions.append(
            (models.Video.is_published & models.Video.is_approved)
        )
        
        # 如果要显示草稿，添加未发布的视频条件
        if show_draft:
            conditions.append(
                (~models.Video.is_published & (models.Video.author_id == current_user.id))
            )
            
        # 如果要显示待审核，添加已发布未审核的视频条件
        if show_pending:
            conditions.append(
                (models.Video.is_published & ~models.Video.is_approved & (models.Video.author_id == current_user.id))
            )
            
        # 使用 OR 连接所有条件
        query = select(models.Video).filter(db.or_(*conditions))
        
        # 获取分页数据
        result = await db.execute(query.offset(skip).limit(limit))
        videos = result.scalars().all()
        
        # 获取总数
        count_result = await db.execute(query)
        total = len(count_result.scalars().all())
        
    return {"total": total, "items": videos}


@router.get("/my", response_model=schemas.VideoList)
async def read_my_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的视频列表"""
    videos = await crud.video.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )
    result = await db.execute(
        select(models.Video).filter(models.Video.author_id == current_user.id)
    )
    total = len(result.scalars().all())
    return {"total": total, "items": videos}


@router.get("/{video_id}", response_model=schemas.Video)
async def read_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取视频详情"""
    video = await video_service.check_permission(db, video_id, current_user)
    return video


@router.put("/{video_id}", response_model=schemas.Video)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新视频
    - 如果视频从未发布变为发布，需要创建审核记录
    - 如果已发布视频的内容有更新，需要重新审核
    - 未发布的视频（草稿）可以自由修改，不需要审核
    """
    # 检查更新权限
    video = await video_service.check_update_permission(db, video_id, current_user)

    video_in_dict = video_in.dict(exclude_unset=True)
    
    # 处理发布状态变更
    if "is_published" in video_in_dict:
        video = await video_service.handle_publish_status(
            db=db, content=video, is_published=video_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(video_in.title or video_in.description or video_in.url)
    video = await video_service.handle_content_update(
        db=db, content=video, has_content_update=has_content_update
    )

    # 更新视频
    video = await crud.video.update(db=db, db_obj=video, obj_in=video_in_dict)
    return video


@router.put("/{video_id}/move", response_model=schemas.Video)
async def move_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    folder_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """移动视频到指定文件夹"""
    # 检查移动权限
    video = await video_service.check_move_permission(
        db=db, video_id=video_id, folder_id=folder_id, current_user=current_user
    )
    # 移动视频
    video = await crud.video.update(
        db=db,
        db_obj=video,
        obj_in={"folder_id": folder_id},
    )
    return video


@router.delete("/{video_id}", response_model=schemas.Video)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """删除视频"""
    # 检查删除权限
    video = await video_service.check_delete_permission(db, video_id, current_user)
    # 删除视频及其关联数据
    video = await video_service.delete_content(db, video)
    return video
