import json
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """重写全局响应格式化中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        response = await call_next(request)
        # 对于 204 状态码，直接返回原始响应
        if response.status_code == 204:
            return None

        if (
            request.url.path.startswith("/api/v1")
            and response.status_code < 400
            and response.status_code != 204
            and request.url.path not in ["/api/v1/health"]
        ):
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                content = json.loads(body.decode("utf-8")) if body else {}
                formatted_content = {"status": "success", "data": content}
                response_body = json.dumps(
                    formatted_content, ensure_ascii=False, default=str
                ).encode("utf-8")
                response.headers["Content-Length"] = str(len(response_body))
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    media_type=response.media_type,
                )
            except Exception:
                return Response(
                    content="Internal Server Error",
                    status_code=500,
                    headers=dict(response.headers),
                    media_type=response.media_type,
                )
        return response
